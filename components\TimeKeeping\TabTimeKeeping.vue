<template>
  <div
    class="bg-white rounded-lg border border-gray-200 md:h-screen-50 flex flex-col"
  >
    <!-- Tab Navigation -->
    <div class="border-b border-gray-200 px-4 py-3">
      <TabNavigation
        v-model="activeTab"
        :tabs="tabs"
        @tab-change="handleTabChange"
      />
    </div>

    <!-- Tab Content -->
    <div class="flex-1 overflow-hidden">
      <Transition name="tab-fade" mode="out-in">
        <div :key="activeTab" class="h-full">
          <!-- Work Content Tab -->
          <div v-if="activeTab === 'work-content'" class="h-full flex flex-col">
            <WorkContentTab
              :loading="loading"
              @task-updated="handleTaskUpdated"
            />
          </div>

          <!-- Time Tracking Tab -->
          <div
            v-else-if="activeTab === 'tracking'"
            class="h-full flex flex-col"
          >
            <TimeTrackingContent
              :loading="loading"
              @start-tracking="handleStartTracking"
              @stop-tracking="handleStopTracking"
              @add-break="handleAddBreak"
              @end-break="handleEndBreak"
            />
          </div>
        </div>
      </Transition>
    </div>

    <!-- Loading Overlay -->
    <Transition name="fade">
      <div
        v-if="loading"
        class="absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-10"
      >
        <div class="flex flex-col items-center gap-3">
          <div
            class="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin"
          ></div>
          <p class="text-sm text-gray-600">{{ loadingText }}</p>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from "vue";
import { ClockIcon, DocumentTextIcon } from "@heroicons/vue/24/outline";

// Types
interface Tab {
  value: string;
  label: string;
  icon: any;
}

// Reactive state
const activeTab = ref<string>("work-content");
const loading = ref<boolean>(false);
const loadingText = ref<string>("Đang tải...");

// Tab configuration - chỉ có 2 tab
const tabs = ref<Tab[]>([
  {
    value: "work-content",
    label: "Công việc",
    icon: DocumentTextIcon,
  },
  {
    value: "tracking",
    label: "Chấm công",
    icon: ClockIcon,
  },
]);

// Composables
const { createWorkEfforts } = useTimeKeeping();

// Methods
const handleTabChange = (tab: any) => {
  if (loading.value) return;

  activeTab.value = tab.value;
  loadTabContent(tab.value);
};

const handleTaskUpdated = (task: any) => {
  console.log("Task updated:", task);
  // Handle task update logic here
};

const loadTabContent = async (tabValue: string) => {
  loading.value = true;

  try {
    switch (tabValue) {
      case "work-content":
        loadingText.value = "Đang tải nội dung công việc...";
        await loadWorkContent();
        break;
      case "tracking":
        loadingText.value = "Đang tải thông tin chấm công...";
        await loadCurrentTimeTracking();
        break;
    }
  } catch (error) {
    console.error("Error loading tab content:", error);
  } finally {
    loading.value = false;
  }
};

const loadWorkContent = async () => {
  // Load work content data
  await new Promise((resolve) => setTimeout(resolve, 500)); // Simulate API call
};

const loadCurrentTimeTracking = async () => {
  // Load current time tracking status
  await new Promise((resolve) => setTimeout(resolve, 500)); // Simulate API call
};

// Time tracking handlers
const handleStartTracking = async (data: any) => {
  loading.value = true;
  loadingText.value = "Đang bắt đầu chấm công...";

  try {
    const userId = useCookie("userId").value;
    if (!userId) {
      throw new Error("User ID not found");
    }
    await createWorkEfforts(
      userId,
      "Check In",
      "Bắt đầu ca làm việc",
      "CHECK_IN",
      "TIMEKEEPING",
      data.attributes || {},
      data.attachment || {},
      ""
    );

    // Reload current tracking data
    await loadCurrentTimeTracking();
  } catch (error) {
    console.error("Error starting time tracking:", error);
  } finally {
    loading.value = false;
  }
};

const handleStopTracking = async (data: any) => {
  loading.value = true;
  loadingText.value = "Đang kết thúc chấm công...";

  try {
    const userId = useCookie("userId").value;
    if (!userId) {
      throw new Error("User ID not found");
    }
    await createWorkEfforts(
      userId,
      "Check Out",
      "Kết thúc ca làm việc",
      "CHECK_OUT",
      "TIMEKEEPING",
      data.attributes || {},
      data.attachment || {},
      ""
    );

    // Reload current tracking data
    await loadCurrentTimeTracking();
  } catch (error) {
    console.error("Error stopping time tracking:", error);
  } finally {
    loading.value = false;
  }
};

const handleAddBreak = async (data: any) => {
  // Handle break start
  console.log("Adding break:", data);
};

const handleEndBreak = async (data: any) => {
  // Handle break end
  console.log("Ending break:", data);
};

// Lifecycle
onMounted(() => {
  loadTabContent(activeTab.value);
});

// Watch for tab changes
watch(activeTab, (newTab) => {
  // Optional: Save current tab to localStorage for persistence
  localStorage.setItem("timeKeeping_activeTab", newTab);
});

// Restore last active tab on mount
onMounted(() => {
  const savedTab = localStorage.getItem("timeKeeping_activeTab");
  if (savedTab && tabs.value.some((tab) => tab.value === savedTab)) {
    activeTab.value = savedTab;
  }
});
</script>

<style scoped>
/* Tab transition animations */
.tab-fade-enter-active,
.tab-fade-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tab-fade-enter-from {
  opacity: 0;
  transform: translateY(10px);
}

.tab-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Loading overlay transition */
.fade-enter-active,
.fade-leave-active {
  transition: all 0.2s ease-in-out;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Tab button styling */
button {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

button:active {
  transform: scale(0.98);
}

/* Focus styles for accessibility */
button:focus-visible {
  outline: 2px solid #3f51b5;
  outline-offset: 2px;
}

/* Tab navigation responsive */
@media (max-width: 768px) {
  .px-4 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }
}

/* Loading spinner animation */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Backdrop blur fallback */
@supports not (backdrop-filter: blur(12px)) {
  .backdrop-blur-sm {
    background-color: rgba(255, 255, 255, 0.9);
  }
}
</style>
