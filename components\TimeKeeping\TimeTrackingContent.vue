<template>
  <div class="h-full flex flex-col p-4 space-y-4">
    <!-- Current Status Card -->
    <div
      class="bg-gradient-to-r from-primary-light to-primary-dark rounded-lg p-6 text-white"
    >
      <div class="flex items-center justify-between">
        <div>
          <h3 class="text-lg font-semibold mb-2">Trạng thái hiện tại</h3>
          <div class="flex items-center gap-2">
            <div :class="getStatusIndicatorClass(currentStatus)"></div>
            <span class="text-sm">{{ getStatusText(currentStatus) }}</span>
          </div>
        </div>
        <div class="text-right">
          <div class="text-2xl font-bold">{{ currentTime }}</div>
          <div class="text-sm opacity-90">{{ currentDate }}</div>
        </div>
      </div>
    </div>

    <!-- Time Tracking Controls -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <!-- Check In/Out Card -->
      <div class="bg-white rounded-lg border border-gray-200 p-6">
        <div class="flex items-center gap-3 mb-4">
          <div
            class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center"
          >
            <ClockIcon class="w-5 h-5 text-green-600" />
          </div>
          <div>
            <h4 class="font-semibold text-gray-900">Chấm công</h4>
            <p class="text-sm text-gray-500">Bắt đầu/Kết thúc ca làm việc</p>
          </div>
        </div>

        <div class="space-y-3">
          <button
            v-if="currentStatus === 'not_started'"
            @click="handleStartWork"
            :disabled="loading"
            class="w-full bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-lg font-medium transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <div class="flex items-center justify-center gap-2">
              <PlayIcon class="w-4 h-4" />
              Bắt đầu ca làm việc
            </div>
          </button>

          <button
            v-else-if="currentStatus === 'working' || currentStatus === 'break'"
            @click="handleEndWork"
            :disabled="loading"
            class="w-full bg-red-600 hover:bg-red-700 text-white py-3 px-4 rounded-lg font-medium transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <div class="flex items-center justify-center gap-2">
              <StopIcon class="w-4 h-4" />
              Kết thúc ca làm việc
            </div>
          </button>

          <div v-else class="text-center py-3 text-gray-500">
            Ca làm việc đã kết thúc
          </div>
        </div>
      </div>

      <!-- Break Controls Card -->
      <div class="bg-white rounded-lg border border-gray-200 p-6">
        <div class="flex items-center gap-3 mb-4">
          <div
            class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center"
          >
            <PauseIcon class="w-5 h-5 text-orange-600" />
          </div>
          <div>
            <h4 class="font-semibold text-gray-900">Nghỉ giải lao</h4>
            <p class="text-sm text-gray-500">Tạm dừng/Tiếp tục làm việc</p>
          </div>
        </div>

        <div class="space-y-3">
          <button
            v-if="currentStatus === 'working'"
            @click="handleStartBreak"
            :disabled="loading"
            class="w-full bg-orange-600 hover:bg-orange-700 text-white py-3 px-4 rounded-lg font-medium transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <div class="flex items-center justify-center gap-2">
              <PauseIcon class="w-4 h-4" />
              Bắt đầu nghỉ giải lao
            </div>
          </button>

          <button
            v-else-if="currentStatus === 'break'"
            @click="handleEndBreak"
            :disabled="loading"
            class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg font-medium transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <div class="flex items-center justify-center gap-2">
              <PlayIcon class="w-4 h-4" />
              Kết thúc nghỉ giải lao
            </div>
          </button>

          <div v-else class="text-center py-3 text-gray-500">
            {{
              currentStatus === "not_started"
                ? "Chưa bắt đầu ca làm việc"
                : "Không thể nghỉ giải lao"
            }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from "vue";
import { format } from "date-fns";
import { vi } from "date-fns/locale";
import {
  ClockIcon,
  PlayIcon,
  StopIcon,
  PauseIcon,
  DocumentTextIcon,
  ArrowDownTrayIcon,
  CogIcon,
} from "@heroicons/vue/24/outline";

// Props
interface Props {
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
});

// Emits
const emit = defineEmits<{
  startTracking: [data: any];
  stopTracking: [data: any];
  addBreak: [data: any];
  endBreak: [data: any];
}>();

// Reactive state
const currentTime = ref<string>("");
const currentDate = ref<string>("");
const currentStatus = ref<"not_started" | "working" | "break" | "completed">(
  "not_started"
);
const timeInterval = ref<NodeJS.Timeout | null>(null);

// Mock data for today's summary
const todaySummary = ref({
  workHours: "7.5h",
  breakHours: "1.0h",
  checkIns: 2,
  efficiency: 95,
});

// Computed
const getStatusIndicatorClass = computed(() => (status: string) => {
  const baseClass = "w-3 h-3 rounded-full";
  switch (status) {
    case "working":
      return `${baseClass} bg-green-400 animate-pulse`;
    case "break":
      return `${baseClass} bg-orange-400 animate-pulse`;
    case "completed":
      return `${baseClass} bg-blue-400`;
    default:
      return `${baseClass} bg-gray-400`;
  }
});

const getStatusText = computed(() => (status: string) => {
  switch (status) {
    case "working":
      return "Đang làm việc";
    case "break":
      return "Đang nghỉ giải lao";
    case "completed":
      return "Đã hoàn thành";
    default:
      return "Chưa bắt đầu";
  }
});

// Methods
const updateTime = () => {
  const now = new Date();
  currentTime.value = format(now, "HH:mm:ss", { locale: vi });
  currentDate.value = format(now, "EEEE, dd/MM/yyyy", { locale: vi });
};

const handleStartWork = () => {
  currentStatus.value = "working";
  emit("startTracking", {
    timestamp: Date.now(),
    type: "CHECK_IN",
    attributes: {
      location: "Office",
      device: "Web",
    },
  });
};

const handleEndWork = () => {
  currentStatus.value = "completed";
  emit("stopTracking", {
    timestamp: Date.now(),
    type: "CHECK_OUT",
    attributes: {
      location: "Office",
      device: "Web",
    },
  });
};

const handleStartBreak = () => {
  currentStatus.value = "break";
  emit("addBreak", {
    timestamp: Date.now(),
    type: "BREAK_START",
  });
};

const handleEndBreak = () => {
  currentStatus.value = "working";
  emit("endBreak", {
    timestamp: Date.now(),
    type: "BREAK_END",
  });
};

const handleViewHistory = () => {
  // Emit event to parent to switch to history tab
  console.log("View history clicked");
};

const handleExportReport = () => {
  // Emit event to parent to export report
  console.log("Export report clicked");
};

const handleSettings = () => {
  // Emit event to parent to switch to settings tab
  console.log("Settings clicked");
};

// Lifecycle
onMounted(() => {
  updateTime();
  timeInterval.value = setInterval(updateTime, 1000);
});

onUnmounted(() => {
  if (timeInterval.value) {
    clearInterval(timeInterval.value);
  }
});
</script>

<style scoped>
/* Additional component-specific styles */
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>
