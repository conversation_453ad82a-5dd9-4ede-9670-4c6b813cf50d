<template>
  <div class="h-full flex flex-col p-4 space-y-4">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <h3 class="text-lg font-semibold text-gray-900">Chi tiết công việc</h3>
    </div>
    <TiptapEditor
      @update:modelValue="handleUpdateContent"
      :modelValue="'em nè nè nè'"
      :placeholder="'nhập nội dung công việc'"
      :showCharacterCount="true"
      :editable="true"
    ></TiptapEditor>
  </div>
</template>

<script setup lang="ts">
const handleUpdateContent = (content: string) => {
  console.log("content", content);
};
</script>
